package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3

import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3CommonPrefixesListingException
import com.decurret_dcp.dcjpy.bcmonitoring.exception.S3Exception
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import software.amazon.awssdk.awscore.exception.AwsErrorDetails
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.*
import spock.lang.Specification

class S3ClientAdaptorSpec extends Specification {

	S3Client mockS3Client
	LoggingService mockLogger
	S3ClientAdaptor s3ClientAdaptor

	def setup() {
		mockS3Client = Mock(S3Client)
		mockLogger = Mock(LoggingService)
		s3ClientAdaptor = new S3ClientAdaptor(mockS3Client, mockLogger)
	}

	def "should get object from S3"() {
		given:
		def bucket = "test-bucket"
		def key = "test-key"
		def content = "test content".getBytes()
		def responseInputStream = Mock(ResponseInputStream)

		when:
		def result = s3ClientAdaptor.getObject(bucket, key)

		then:
		1 * mockS3Client.getObject(_ as GetObjectRequest) >> { GetObjectRequest request ->
			assert request.bucket() == bucket
			assert request.key() == key
			responseInputStream
		}
		1 * responseInputStream.readAllBytes() >> content
		result.readAllBytes() == content
	}

	def "should handle exception when getting object from S3"() {
		given:
		def bucket = "test-bucket"
		def key = "test-key"
		def s3Exception = software.amazon.awssdk.services.s3.model.S3Exception.builder().build()

		when:
		s3ClientAdaptor.getObject(bucket, key)

		then:
		1 * mockS3Client.getObject(_ as GetObjectRequest) >> { throw s3Exception }
		1 * mockLogger.error(_ as String, s3Exception)
		def exception = thrown(S3Exception)
		exception.cause == s3Exception
	}

	def "should list common prefixes of objects"() {
		given:
		def bucket = "test-bucket"
		def delimiter = "/"
		def prefixes = [
			CommonPrefix.builder().prefix("prefix1/").build(),
			CommonPrefix.builder().prefix("prefix2/").build()
		]
		def response = ListObjectsV2Response.builder().commonPrefixes(prefixes).build()

		when:
		def commonPrefixes = s3ClientAdaptor.listCommonPrefixesObjects(bucket, delimiter)

		then:
		1 * mockS3Client.listObjectsV2(_ as ListObjectsV2Request) >> { ListObjectsV2Request request ->
			assert request.bucket() == bucket
			assert request.delimiter() == delimiter
			response
		}
		commonPrefixes == prefixes
	}

	def "should handle empty common prefixes"() {
		given:
		def bucket = "test-bucket"
		def delimiter = "/"
		def response = ListObjectsV2Response.builder().commonPrefixes([]).build()

		when:
		def commonPrefixes = s3ClientAdaptor.listCommonPrefixesObjects(bucket, delimiter)

		then:
		1 * mockS3Client.listObjectsV2(_ as ListObjectsV2Request) >> response
		commonPrefixes == []
	}

	def "should list objects with prefix"() {
		given:
		def bucket = "test-bucket"
		def prefix = "test-prefix"
		def keys = [
			"test-prefix/key1",
			"test-prefix/key2"
		]

		def expected = ListObjectsV2Response.builder()
				.contents([
					S3Object.builder().key("test-prefix/key1").build(),
					S3Object.builder().key("test-prefix/key2").build()
				])
				.build()

		def s3Objects = keys.collect { key ->
			S3Object.builder().key(key).build()
		}
		def response = ListObjectsV2Response.builder().contents(s3Objects).build()

		when:
		def objectKeys = s3ClientAdaptor.listObjects(bucket, prefix)

		then:
		1 * mockS3Client.listObjectsV2(_ as ListObjectsV2Request) >> { ListObjectsV2Request request ->
			assert request.bucket() == bucket
			assert request.prefix() == prefix
			response
		}
		objectKeys == expected
	}

	def "should handle exception when listing objects"() {
		given:
		def bucket = "test-bucket"
		def prefix = "test-prefix"
		def s3Exception = software.amazon.awssdk.services.s3.model.S3Exception.builder().build()

		when:
		s3ClientAdaptor.listObjects(bucket, prefix)

		then:
		1 * mockS3Client.listObjectsV2(_ as ListObjectsV2Request) >> { throw s3Exception }
		1 * mockLogger.error(_ as String, s3Exception)
		def exception = thrown(S3Exception)
		exception.cause == s3Exception
	}

	def "should handle generic exception when getting object from S3"() {
		given:
		def bucket = "test-bucket"
		def key = "test-key"
		def responseInputStream = Mock(ResponseInputStream)
		def ioException = new IOException("IO error")

		when:
		s3ClientAdaptor.getObject(bucket, key)

		then:
		1 * mockS3Client.getObject(_ as GetObjectRequest) >> responseInputStream
		1 * responseInputStream.readAllBytes() >> { throw ioException }
		1 * mockLogger.error(_ as String, ioException)
		def exception = thrown(S3Exception)
		exception.cause == ioException
	}

	def "should handle exception with message when getting object"() {
		given:
		def bucket = "test-bucket"
		def key = "test-key"
		def errorMessage = "Access denied"
		def awsErrorDetails = AwsErrorDetails.builder().errorMessage(errorMessage).build()
		def s3Exception = software.amazon.awssdk.services.s3.model.S3Exception.builder().awsErrorDetails(awsErrorDetails).build()

		when:
		s3ClientAdaptor.getObject(bucket, key)

		then:
		1 * mockS3Client.getObject(_ as GetObjectRequest) >> { throw s3Exception }
		1 * mockLogger.error(_ as String, s3Exception)
		def exception = thrown(S3Exception)
		exception.cause == s3Exception
	}

	def "should handle exception with message when listing common prefixes"() {
		given:
		def bucket = "test-bucket"
		def delimiter = "/"
		def errorMessage = "Bucket not found"
		def awsErrorDetails = AwsErrorDetails.builder().errorMessage(errorMessage).build()
		def s3Exception = software.amazon.awssdk.services.s3.model.S3Exception.builder().awsErrorDetails(awsErrorDetails).build()

		when:
		s3ClientAdaptor.listCommonPrefixesObjects(bucket, delimiter)

		then:
		1 * mockS3Client.listObjectsV2(_ as ListObjectsV2Request) >> { throw s3Exception }
		1 * mockLogger.error(_ as String, s3Exception)
		def exception = thrown(S3CommonPrefixesListingException)
		exception.cause == s3Exception
	}

	def "should handle exception with message when listing objects"() {
		given:
		def bucket = "test-bucket"
		def prefix = "test-prefix"
		def errorMessage = "Permission denied"
		def awsErrorDetails = AwsErrorDetails.builder().errorMessage(errorMessage).build()
		def s3Exception = software.amazon.awssdk.services.s3.model.S3Exception.builder().awsErrorDetails(awsErrorDetails).build()

		when:
		s3ClientAdaptor.listObjects(bucket, prefix)

		then:
		1 * mockS3Client.listObjectsV2(_ as ListObjectsV2Request) >> { throw s3Exception }
		1 * mockLogger.error(_ as String, s3Exception)
		def exception = thrown(S3Exception)
		exception.cause == s3Exception
	}
}
