./gradlew clean check testAdhoc

> Task :spotlessGroovy
Missing required bundle org.eclipse.jdt.debug needed by [org.eclipse.jdt.launching, org.eclipse.jdt.launching.macosx]
Missing required capability osgi.extender:osgi.extender=osgi.serviceloader.processor needed by org.apache.commons.commons-logging
Missing required capability osgi.serviceloader:osgi.serviceloader=org.apache.commons.logging.LogFactory needed by org.apache.commons.commons-logging
Missing required capability osgi.serviceloader:osgi.serviceloader=org.apache.juli.logging.Log needed by org.mortbay.jasper.apache-jsp
Missing required capability osgi.extender:osgi.extender=osgi.serviceloader.processor needed by org.mortbay.jasper.apache-jsp
Recommend setting osgi.configuration.area to a directory, getDataFile will return null
Starting Groovy-Eclipse compiler resolver. Specified compiler level: unspecified
129 org.codehaus.groovy_5.0.0.v202406302347-e2406 RESOLVED

> Task :compileJava
ノート: /Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java/src/main/java/com/decurret_dcp/dcjpy/bcmonitoring/config/S3Config.javaは推奨されないAPIを使用またはオーバーライドしています。
ノート: 詳細は、-Xlint:deprecationオプションを指定して再コンパイルしてください。
OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended

> Task :test

should get object from S3 STARTED

should get object from S3 PASSED

should handle exception when getting object from S3 STARTED

should handle exception when getting object from S3 PASSED

should list common prefixes of objects STARTED

should list common prefixes of objects PASSED

should handle empty common prefixes STARTED

should handle empty common prefixes PASSED

should list objects with prefix STARTED

should list objects with prefix PASSED

should handle exception when listing objects STARTED

should handle exception when listing objects PASSED

should handle generic exception when getting object from S3 STARTED

should handle generic exception when getting object from S3 PASSED

should handle exception with message when getting object STARTED

should handle exception with message when getting object PASSED

should handle exception with message when listing common prefixes STARTED

should handle exception with message when listing common prefixes PASSED

should handle exception with message when listing objects STARTED

should handle exception with message when listing objects PASSED

should throw ConfigurationException when S3 bucket name is empty STARTED

should throw ConfigurationException when S3 bucket name is empty PASSED

should throw ConfigurationException when S3 bucket name is null STARTED

should throw ConfigurationException when S3 bucket name is null PASSED

should process multiple prefixes, objects and download json files STARTED

should process multiple prefixes, objects and download json files PASSED

should process single prefix, objects and download json files STARTED

should process single prefix, objects and download json files PASSED

should skip non-json files STARTED

should skip non-json files PASSED

should handle empty object list STARTED

should handle empty object list PASSED

should throw S3Exception when listing prefixes fails STARTED

should throw S3Exception when listing prefixes fails PASSED

should throw S3Exception when listing objects fails STARTED

should throw S3Exception when listing objects fails PASSED

should only process direct child objects STARTED

should only process direct child objects PASSED

should throw S3Exception when object retrieval fails STARTED

should throw S3Exception when object retrieval fails PASSED

should throw IOException when parsing fails STARTED

should throw IOException when parsing fails PASSED

should process single prefix, name start with dot STARTED

should process single prefix, name start with dot PASSED

execute should process one iteration and terminate when running is set to false STARTED

execute should process one iteration and terminate when running is set to false PASSED

execute should catch NumberFormatException and log error when checkInterval is invalid STARTED

execute should catch NumberFormatException and log error when checkInterval is invalid PASSED

execute should handle exceptions in monitoring loop STARTED

execute should handle exceptions in monitoring loop PASSED

execute should catch Exception in monitoring loop, log error, and continue execution STARTED

execute should catch Exception in monitoring loop, log error, and continue execution PASSED

monitorEvents should process block height and transactions STARTED

monitorEvents should process block height and transactions PASSED

monitorEvents should handle exceptions STARTED

monitorEvents should handle exceptions PASSED

monitorEvents should catch Exception, log error, and continue execution STARTED

monitorEvents should catch Exception, log error, and continue execution PASSED

monitorEvents should process new transactions when finalBlockHeight is valid STARTED

monitorEvents should process new transactions when finalBlockHeight is valid PASSED

processPendingTransactions should process transactions and update block height STARTED

processPendingTransactions should process transactions and update block height PASSED

processPendingTransactions should handle empty queue STARTED

processPendingTransactions should handle empty queue PASSED

processPendingTransactions should handle block height change STARTED

processPendingTransactions should handle block height change PASSED

processPendingTransactions should handle zero block height STARTED

processPendingTransactions should handle zero block height PASSED

processPendingTransactions should handle save failures STARTED

processPendingTransactions should handle save failures PASSED

processPendingTransactions should handle interrupted exception STARTED

processPendingTransactions should handle interrupted exception PASSED

processPendingTransactions should handle block height save failure at end of queue STARTED

processPendingTransactions should handle block height save failure at end of queue PASSED

processPendingTransactions should handle block height save failure on block change STARTED

processPendingTransactions should handle block height save failure on block change PASSED

processPendingTransactions should not save block height when consecutive transactions have same block height STARTED

processPendingTransactions should not save block height when consecutive transactions have same block height PASSED

saveTransaction should handle empty transaction hash STARTED

saveTransaction should handle empty transaction hash PASSED

saveTransaction should handle null transaction hash STARTED

saveTransaction should handle null transaction hash PASSED

saveTransaction should save events and block height STARTED

saveTransaction should save events and block height PASSED

saveTransaction should handle event save failure STARTED

saveTransaction should handle event save failure PASSED

saveTransaction should handle block height save failure STARTED

saveTransaction should handle block height save failure PASSED

fetchTraceId should handle valid JSON STARTED

fetchTraceId should handle valid JSON PASSED

fetchTraceId should handle empty traceId STARTED

fetchTraceId should handle empty traceId PASSED

fetchTraceId should handle null traceId STARTED

fetchTraceId should handle null traceId PASSED

fetchTraceId should handle JSON parsing exception STARTED

fetchTraceId should handle JSON parsing exception PASSED

fetchTraceId should skip zero bytes when building trace ID string STARTED

fetchTraceId should skip zero bytes when building trace ID string PASSED

processNewTransactions should process transactions successfully STARTED

processNewTransactions should process transactions successfully PASSED

processNewTransactions should process transactions is empty STARTED

processNewTransactions should process transactions is empty PASSED

processNewTransactions should exit when block height is zero STARTED

processNewTransactions should exit when block height is zero PASSED

processNewTransactions should exit when saveTransaction returns false STARTED

processNewTransactions should exit when saveTransaction returns false PASSED

processNewTransactions should handle InterruptedException STARTED

processNewTransactions should handle InterruptedException PASSED

processNewTransactions should exit loop when running is false STARTED

processNewTransactions should exit loop when running is false PASSED

processNewTransactions should exit early when saveTransaction returns false due to block height save failure STARTED

processNewTransactions should exit early when saveTransaction returns false due to block height save failure PASSED

savePendingTransaction should handle empty transaction hash STARTED

savePendingTransaction should handle empty transaction hash PASSED

savePendingTransaction should handle null transaction hash STARTED

savePendingTransaction should handle null transaction hash PASSED

savePendingTransactionBlockNumber should handle block height save failure STARTED

savePendingTransactionBlockNumber should handle block height save failure PASSED

monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height STARTED

monitorEvents should execute processNewTransactions when processPendingTransactions returns valid block height PASSED

monitorEvents should log error when exception occurs in monitoring process STARTED

monitorEvents should log error when exception occurs in monitoring process PASSED

monitorEvents should log error when exception escapes from executor task STARTED

monitorEvents should log error when exception escapes from executor task PASSED

monitorEvents should handle exceptions in processNewTransactions STARTED

monitorEvents should handle exceptions in processNewTransactions PASSED

monitorEvents should log error when exception occurs while getting filter logs STARTED

monitorEvents should log error when exception occurs while getting filter logs PASSED

monitorEvents should not call processNewTransactions when processPendingTransactions returns null STARTED

monitorEvents should not call processNewTransactions when processPendingTransactions returns null PASSED

monitorEvents should run without errors when all operations succeed STARTED

monitorEvents should run without errors when all operations succeed PASSED

savePendingTransaction should return false when eventRepository.save fails STARTED

savePendingTransaction should return false when eventRepository.save fails PASSED

processNewTransactions should handle null transaction from queue STARTED

processNewTransactions should handle null transaction from queue PASSED

processNewTransactions should handle websocket disconnection (block number -1) STARTED

processNewTransactions should handle websocket disconnection (block number -1) PASSED

processNewTransactions should handle saveTransaction failure STARTED

processNewTransactions should handle saveTransaction failure PASSED

sleep should handle InterruptedException STARTED

sleep should handle InterruptedException PASSED

saveTransaction should handle StructuredLogContext close exception STARTED

saveTransaction should handle StructuredLogContext close exception PASSED

savePendingTransaction should handle StructuredLogContext close exception STARTED

savePendingTransaction should handle StructuredLogContext close exception PASSED

processNewTransactions should continue when saveTransaction succeeds STARTED

processNewTransactions should continue when saveTransaction succeeds PASSED

saveTransaction should complete successfully with all operations STARTED

saveTransaction should complete successfully with all operations PASSED

savePendingTransaction should complete successfully with all operations STARTED

savePendingTransaction should complete successfully with all operations PASSED

saveTransaction should handle all edge cases in try-with-resources STARTED

saveTransaction should handle all edge cases in try-with-resources PASSED

savePendingTransaction should handle all edge cases in try-with-resources STARTED

savePendingTransaction should handle all edge cases in try-with-resources PASSED

monitorEvents should handle exception when getting block height STARTED

monitorEvents should handle exception when getting block height PASSED

saveTransaction should handle multiple events in transaction STARTED

saveTransaction should handle multiple events in transaction PASSED

savePendingTransaction should handle multiple events in transaction STARTED

savePendingTransaction should handle multiple events in transaction PASSED

saveTransaction should fail on first event when multiple events exist STARTED

saveTransaction should fail on first event when multiple events exist PASSED

savePendingTransaction should fail on second event when multiple events exist STARTED

savePendingTransaction should fail on second event when multiple events exist PASSED

processPendingTransactions should handle first transaction with non-zero block height STARTED

processPendingTransactions should handle first transaction with non-zero block height PASSED

saveTransaction should handle transaction with empty events list STARTED

saveTransaction should handle transaction with empty events list PASSED

savePendingTransaction should handle transaction with empty events list STARTED

savePendingTransaction should handle transaction with empty events list PASSED

saveTransaction should handle exception in try-with-resources close STARTED

saveTransaction should handle exception in try-with-resources close PASSED

savePendingTransaction should handle exception in try-with-resources close STARTED

savePendingTransaction should handle exception in try-with-resources close PASSED

should successfully get block height STARTED

should successfully get block height PASSED

should return 0 when no block heights found STARTED

should return 0 when no block heights found PASSED

should throw DataAccessException when get fails STARTED

should throw DataAccessException when get fails PASSED

should successfully save block height STARTED

should successfully save block height PASSED

should handle exception when saving block height fails STARTED

should handle exception when saving block height fails PASSED

should handle null block height gracefully STARTED

should handle null block height gracefully PASSED

should successfully save an event STARTED

should successfully save an event PASSED

should handle exception when saving event fails STARTED

should handle exception when saving event fails PASSED

should throw NPE when handling null event STARTED

should throw NPE when handling null event PASSED

should attempt to save event even with empty attribute map STARTED

should attempt to save event even with empty attribute map PASSED

convBlock2EventEntities should handle empty transaction lists STARTED

convBlock2EventEntities should handle empty transaction lists PASSED

convBlock2EventEntities should handle missing transaction receipts STARTED

convBlock2EventEntities should handle missing transaction receipts PASSED

convBlock2EventEntities should process transactions with valid logs STARTED

convBlock2EventEntities should process transactions with valid logs PASSED

convBlock2EventEntities should process transactions with logs event transactionHash null STARTED

convBlock2EventEntities should process transactions with logs event transactionHash null PASSED

convBlock2EventEntities should handle exceptions during log processing STARTED

convBlock2EventEntities should handle exceptions during log processing PASSED

convBlock2EventEntities should handle exceptions during transaction processing STARTED

convBlock2EventEntities should handle exceptions during transaction processing PASSED

convBlock2EventEntities should skip events with empty transaction hash STARTED

convBlock2EventEntities should skip events with empty transaction hash PASSED

getPendingTransactions should handle exceptions during event processing STARTED

getPendingTransactions should handle exceptions during event processing PASSED

isDelayed should detect delayed blocks STARTED

isDelayed should detect delayed blocks PASSED

getPendingTransactions should process logs and return transactions STARTED

getPendingTransactions should process logs and return transactions PASSED

getPendingTransactions should process logs with valid data STARTED

getPendingTransactions should process logs with valid data PASSED

getPendingTransactions should handle log processing errors STARTED

getPendingTransactions should handle log processing errors PASSED

getPendingTransactions should handle general log processing errors STARTED

getPendingTransactions should handle general log processing errors PASSED

getPendingTransactions should handle exceptions STARTED

getPendingTransactions should handle exceptions PASSED

getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true STARTED

getPendingTransactions should log 'Error processing pending transactions' when forceOuterError is true PASSED

getPendingTransactions should process a log entry correctly STARTED

getPendingTransactions should process a log entry correctly PASSED

should get block timestamp correctly STARTED

should get block timestamp correctly PASSED

subscribeAll should subscribe to contract events STARTED

subscribeAll should subscribe to contract events PASSED

subscribeAll should subscribe to block events STARTED

subscribeAll should subscribe to block events PASSED

subscribeAll should skip processing for delayed blocks STARTED

subscribeAll should skip processing for delayed blocks PASSED

subscribeAll should process non-delayed blocks with events STARTED

subscribeAll should process non-delayed blocks with events PASSED

subscribeAll should handle exceptions during block processing with events STARTED

subscribeAll should handle exceptions during block processing with events PASSED

subscribeAll should events is empty when processing with events STARTED

subscribeAll should events is empty when processing with events PASSED

subscribeAll should add transaction to queue when events are found STARTED

subscribeAll should add transaction to queue when events are found PASSED

subscribeAll should not add transaction to queue when no events are found STARTED

subscribeAll should not add transaction to queue when no events are found PASSED

convertEthLogToEventEntity should successfully convert a log to an event with ABI event STARTED

convertEthLogToEventEntity should successfully convert a log to an event with ABI event PASSED

convertEthLogToEventEntity should failed convert a log with EventValues is null  STARTED

convertEthLogToEventEntity should failed convert a log with EventValues is null  PASSED

convertEthLogToEventEntity should handle null ABI event STARTED

convertEthLogToEventEntity should handle null ABI event PASSED

convertEthLogToEventEntity should handle block retrieval exception STARTED

convertEthLogToEventEntity should handle block retrieval exception PASSED

convertEthLogToEventEntity should handle ABI parser exception STARTED

convertEthLogToEventEntity should handle ABI parser exception PASSED

convertEthLogToEventEntity should handle empty topics list STARTED

convertEthLogToEventEntity should handle empty topics list PASSED

convBlock2EventEntities should process events from a block with logs STARTED

convBlock2EventEntities should process events from a block with logs PASSED

subscribeAll should handle NumberFormatException when parsing allowable timestamp difference STARTED

subscribeAll should handle NumberFormatException when parsing allowable timestamp difference PASSED

subscribeAll should log subscription error STARTED

subscribeAll should log subscription error PASSED

subscribeAll should handle exception during Web3j subscription creation STARTED

subscribeAll should handle exception during Web3j subscription creation PASSED

unsubscribe should dispose subscription when subscription is not null STARTED

unsubscribe should dispose subscription when subscription is not null PASSED

unsubscribe should handle null subscription gracefully STARTED

unsubscribe should handle null subscription gracefully PASSED

getBlockTimestamp should return correct timestamp STARTED

getBlockTimestamp should return correct timestamp PASSED

getBlockTimestamp should handle IOException STARTED

getBlockTimestamp should handle IOException PASSED

convBlock2EventEntities should handle Web3j creation exception STARTED

convBlock2EventEntities should handle Web3j creation exception PASSED

convertEthLogToEventEntity should handle general exceptions during processing STARTED

convertEthLogToEventEntity should handle general exceptions during processing PASSED

subscribeAll should handle subscription callback with delayed block STARTED

subscribeAll should handle subscription callback with delayed block PASSED

subscribeAll should handle subscription callback with non-delayed block and events STARTED

subscribeAll should handle subscription callback with non-delayed block and events PASSED

subscribeAll should handle subscription callback with empty events STARTED

subscribeAll should handle subscription callback with empty events PASSED

subscribeAll should handle subscription callback exception during block processing STARTED

subscribeAll should handle subscription callback exception during block processing PASSED

subscribeAll should handle subscription completion STARTED

subscribeAll should handle subscription completion PASSED

subscribeAll should execute subscription callback and process block STARTED

subscribeAll should execute subscription callback and process block PASSED

subscribeAll should execute subscription callback and add transaction to queue STARTED

subscribeAll should execute subscription callback and add transaction to queue PASSED

subscribeAll should handle exception in subscription callback STARTED

subscribeAll should handle exception in subscription callback PASSED

subscribeAll should handle empty events in subscription callback STARTED

subscribeAll should handle empty events in subscription callback PASSED

subscribeAll should trigger main subscription callback lambda0 STARTED

subscribeAll should trigger main subscription callback lambda0 PASSED

subscribeAll should trigger InterruptedException in async callback STARTED

subscribeAll should trigger InterruptedException in async callback PASSED

subscribeAll should trigger subscription error callback lambda3 STARTED

subscribeAll should trigger subscription error callback lambda3 PASSED

subscribeAll should handle error callback with proper cleanup STARTED

subscribeAll should handle error callback with proper cleanup PASSED

subscribeAll should trigger main subscription callback lambda0 with real execution STARTED

subscribeAll should trigger main subscription callback lambda0 with real execution PASSED

subscribeAll should cover remaining async callback paths with forced execution STARTED

subscribeAll should cover remaining async callback paths with forced execution PASSED

convBlock2EventEntities should handle null transaction STARTED

convBlock2EventEntities should handle null transaction PASSED

getPendingTransactions should handle forced outer error STARTED

getPendingTransactions should handle forced outer error PASSED

Subscribe should return transactions when successful STARTED

Subscribe should return transactions when successful PASSED

Subscribe should throw BlockchainException when DAO error occurs STARTED

Subscribe should throw BlockchainException when DAO error occurs PASSED

GetFilterLogs should return transactions when successful STARTED

GetFilterLogs should return transactions when successful PASSED

GetFilterLogs should throw BlockchainException when DAO error occurs STARTED

GetFilterLogs should throw BlockchainException when DAO error occurs PASSED

parseAbiContent should parse Truffle format ABI STARTED

parseAbiContent should parse Truffle format ABI PASSED

parseAbiContent should parse Hardhat format ABI STARTED

parseAbiContent should parse Hardhat format ABI PASSED

parseAbiContent should handle missing ABI section STARTED

parseAbiContent should handle missing ABI section PASSED

parseAbiContent should handle invalid JSON STARTED

parseAbiContent should handle invalid JSON PASSED

parseAbi should handle empty ABI JSON STARTED

parseAbi should handle empty ABI JSON PASSED

parseAbi should skip entries with invalid event types STARTED

parseAbi should skip entries with invalid event types PASSED

parseAbi should handle events with missing name STARTED

parseAbi should handle events with missing name PASSED

parseAbi should handle events with empty inputs STARTED

parseAbi should handle events with empty inputs PASSED

parseAbi should handle events with duplicate signatures STARTED

parseAbi should handle events with duplicate signatures PASSED

parseAbi should handle mixed case Solidity types STARTED

parseAbi should handle mixed case Solidity types PASSED

parseAbi should handle a large number of events STARTED

parseAbi should handle a large number of events PASSED

parseAbi should handle null or empty ABI content STARTED

parseAbi should handle null or empty ABI content PASSED

parseAbi should handle invalid JSON STARTED

parseAbi should handle invalid JSON PASSED

parseAbi should handle events with unsupported types STARTED

parseAbi should handle events with unsupported types PASSED

parseAbiContent should handle missing address in networks STARTED

parseAbiContent should handle missing address in networks PASSED

parseAbiContent should handle missing contract name in object key STARTED

parseAbiContent should handle missing contract name in object key PASSED

createTypeReference should handle unsupported types gracefully STARTED

createTypeReference should handle unsupported types gracefully PASSED

parseAbiContent should close input stream after parsing STARTED

parseAbiContent should close input stream after parsing PASSED

parseAbi should skip events with missing or empty names STARTED

parseAbi should skip events with missing or empty names PASSED

parseAbiContent should handle networksNode not being an object STARTED

parseAbiContent should handle networksNode not being an object PASSED

appendContractAddress should add new addresses only once STARTED

appendContractAddress should add new addresses only once PASSED

getABIEventByLog should find and return event for valid log STARTED

getABIEventByLog should find and return event for valid log PASSED

getABIEventByLog should throw exception when contract address not found STARTED

getABIEventByLog should throw exception when contract address not found PASSED

getABIEventByLog should throw exception when event signature not found STARTED

getABIEventByLog should throw exception when event signature not found PASSED

getABIEventByLog should handle case-insensitive matching STARTED

getABIEventByLog should handle case-insensitive matching PASSED

> Task :testAdhoc

Should service start up successfully with command line runner and process ABI files STARTED

OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended

> Task :testAdhoc

Should service start up successfully with command line runner and process ABI files PASSED

Should processes ABI files from multiple zones STARTED

Should processes ABI files from multiple zones PASSED

Should correctly parses ABI files based on truffle environment variable STARTED

Should correctly parses ABI files based on truffle environment variable PASSED

Should skip non-json file STARTED

Should skip non-json file PASSED

Should skip deeply nested files and only process direct child objects STARTED

Should skip deeply nested files and only process direct child objects PASSED

Should start fails when parsing malformed JSON STARTED

Should start fails when parsing malformed JSON PASSED

Should start fails when ABI file lacks required abi section STARTED

Should start fails when ABI file lacks required abi section PASSED

Should start fails when s3 connect timeout STARTED

Should start fails when s3 connect timeout PASSED

Should fails to start when S3 bucket is inaccessible STARTED

Should fails to start when S3 bucket is inaccessible PASSED

Should use default values when optional env variables are missing STARTED

Should use default values when optional env variables are missing PASSED

Should load all configuration properties correctly when env is 'prod' STARTED

Should load all configuration properties correctly when env is 'prod' PASSED

Should load all configuration properties correctly STARTED

Should load all configuration properties correctly PASSED

Should load all configuration properties correctly when env is 'test' STARTED

Should load all configuration properties correctly when env is 'test' PASSED

Should handle invalid subscription check interval value gracefully STARTED

Should handle invalid subscription check interval value gracefully PASSED

Should handle invalid subscription allowable block timestamp diff sec value gracefully STARTED

Should handle invalid subscription allowable block timestamp diff sec value gracefully PASSED

Should detects and processes events from new blockchain blocks STARTED

Should detects and processes events from new blockchain blocks PASSED

Should process pending transactions from specified block height STARTED

Should process pending transactions from specified block height PASSED

Should event data correctly parsed into indexed and non-indexed values STARTED

Should event data correctly parsed into indexed and non-indexed values PASSED

Should extracts traceId from event non-indexed values when present STARTED

Should extracts traceId from event non-indexed values when present PASSED

Should run normally when timestamp is equal or less than allowable block timestamp different seconds STARTED

Should run normally when timestamp is equal or less than allowable block timestamp different seconds PASSED

Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC STARTED

Should event that don't contain traceId are processed successfully and logs with empty traceId in MDC PASSED

Should run normally when timestamp against allowable block timestamp different seconds STARTED

Should run normally when timestamp against allowable block timestamp different seconds PASSED

Should handles events that don't match any loaded ABI definitions STARTED

Should handles events that don't match any loaded ABI definitions PASSED

Should handles websocket disconnect during active subscription STARTED

Should handles websocket disconnect during active subscription PASSED

Should handles websocket connection failure at startup STARTED

Should handles websocket connection failure at startup PASSED

Should rejects events with missing transaction hash in pending events STARTED

Should rejects events with missing transaction hash in pending events PASSED

Should rejects events with empty transaction hash STARTED

Should rejects events with empty transaction hash PASSED

Should rejects events with missing transaction hash in new blocks STARTED

Should rejects events with missing transaction hash in new blocks PASSED

Should rejects events with empty transaction hash in new blocks STARTED

Should rejects events with empty transaction hash in new blocks PASSED

Should rejects blocks with zero block number STARTED

Should rejects blocks with zero block number PASSED

Should start successfully with all dependencies available STARTED

Should start successfully with all dependencies available PASSED

Should automatically reinitialize monitoring if error occurs STARTED

Should automatically reinitialize monitoring if error occurs PASSED

Should start successfully with empty ABI bucket STARTED

Should start successfully with empty ABI bucket PASSED

Should start successfully with empty DynamoDB BlockHeight table STARTED

Should start successfully with empty DynamoDB BlockHeight table PASSED

Should fails to start when required properties are invalid STARTED

Should fails to start when required properties are invalid PASSED

Should restart if DynamoDB connection Error STARTED

Should restart if DynamoDB connection Error PASSED

{"@timestamp":"2025-06-27T14:07:30.371221+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.371459+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.371561+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.371582+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.372527+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.372577+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.372634+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.372647+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.372954+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.37297+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.484109+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.484169+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.484657+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.484672+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.484721+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.48473+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.48507+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485084+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485129+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485143+07:00","@version":"1","message":"http-outgoing-109: Close connection","logger_name":"org.apache.http.impl.conn.DefaultManagedHttpClientConnection","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485192+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485423+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485442+07:00","@version":"1","message":"http-outgoing-130: Close connection","logger_name":"org.apache.http.impl.conn.DefaultManagedHttpClientConnection","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485462+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485508+07:00","@version":"1","message":"Connection manager is shutting down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485541+07:00","@version":"1","message":"http-outgoing-125: Close connection","logger_name":"org.apache.http.impl.conn.DefaultManagedHttpClientConnection","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}
{"@timestamp":"2025-06-27T14:07:30.485562+07:00","@version":"1","message":"Connection manager shut down","logger_name":"org.apache.http.impl.conn.PoolingHttpClientConnectionManager","thread_name":"SpringApplicationShutdownHook","level":"DEBUG","level_value":10000,"application":"bcmonitoring"}

[Incubating] Problems report is available at: file:///Users/<USER>/Documents/My_Work_Space/BCMAdhoc/dcbg-dcjpy-bcmonitoring-java/build/reports/problems/problems-report.html

Deprecated Gradle features were used in this build, making it incompatible with Gradle 9.0.

You can use '--warning-mode all' to show the individual deprecation warnings and determine if they come from your own scripts or plugins.

For more on this, please refer to https://docs.gradle.org/8.13/userguide/command_line_interface.html#sec:command_line_warnings in the Gradle documentation.

BUILD SUCCESSFUL in 7m 43s
18 actionable tasks: 18 executed